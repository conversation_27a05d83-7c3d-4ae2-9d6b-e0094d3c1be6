import React, { useState, useEffect, useCallback } from 'react';
import { ProcessResult } from '../types';
import { LocalStorageService } from '../services/LocalStorageService';
import { formatTime } from '../utils/timeFormatter';
import Icon from './Icon';

// 样式已移至 history-panel.css

interface HistoryPanelProps {
  /** 重用查询回调 */
  onReuseQuery?: (query: string) => void;
  /** 日志记录器 */
  logger?: any;
}

/**
 * 历史记录面板组件
 * 展示批处理历史记录
 */
const HistoryPanel: React.FC<HistoryPanelProps> = ({
  onReuseQuery,
  logger,
}) => {
  // 基础状态
  const [history, setHistory] = useState<ProcessResult[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Button state management
  const [buttonStates, setButtonStates] = useState({
    openAllLinks: { loading: false, success: false, error: false },
    clearHistory: { loading: false, success: false, error: false },
  });

  // 更新按钮状态的辅助函数
  const updateButtonState = useCallback(
    (
      buttonKey: keyof typeof buttonStates,
      state: Partial<typeof buttonStates.openAllLinks>,
    ) => {
      setButtonStates(prev => ({
        ...prev,
        [buttonKey]: { ...prev[buttonKey], ...state },
      }));
    },
    [],
  );

  // 重置按钮状态
  const resetButtonState = useCallback(
    (buttonKey: keyof typeof buttonStates) => {
      setTimeout(() => {
        updateButtonState(buttonKey, { success: false, error: false });
      }, 2000); // 2秒后重置状态
    },
    [updateButtonState],
  );

  // 加载历史记录
  const loadHistory = useCallback(() => {
    setIsLoading(true);
    try {
      // 使用分页历史记录功能
      const pagedResult = LocalStorageService.getPagedHistory({
        page: currentPage,
        pageSize,
        sort: 'date-desc',
      });

      setHistory(pagedResult.items);
      setTotalPages(pagedResult.totalPages);
    } catch (error) {
      console.error('Failed to load history:', error);
      // 如果分页失败，回退到加载所有记录
      const allHistory = LocalStorageService.loadHistory({
        sort: 'date-desc',
      });
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      setHistory(allHistory.slice(startIndex, endIndex));
      setTotalPages(Math.ceil(allHistory.length / pageSize));
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, pageSize]);

  // 首次加载
  useEffect(() => {
    loadHistory();
  }, [loadHistory]);

  // 清空历史记录
  const handleClearHistory = useCallback(async () => {
    try {
      updateButtonState('clearHistory', { loading: true, error: false });

      if (window.confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
        // 模拟异步操作
        await new Promise(resolve => setTimeout(resolve, 500));

        LocalStorageService.clearHistory();
        setHistory([]);

        updateButtonState('clearHistory', { loading: false, success: true });
        resetButtonState('clearHistory');

        if (logger) {
          logger.success('历史记录已清空');
        }
      } else {
        updateButtonState('clearHistory', { loading: false });
      }
    } catch (error) {
      updateButtonState('clearHistory', { loading: false, error: true });
      resetButtonState('clearHistory');
      const errorMessage = `清空历史记录失败: ${(error as Error).message}`;
      console.error(errorMessage);
      if (logger) {
        logger.error(errorMessage);
      }
    }
  }, [updateButtonState, resetButtonState, logger]);

  // 在新标签页打开所有链接
  const handleOpenAllLinks = useCallback(async () => {
    try {
      updateButtonState('openAllLinks', { loading: true, error: false });

      const successItems = history.filter(
        item => item.status === 'success' && item.playgroundUrl,
      );

      if (successItems.length === 0) {
        alert('没有可打开的链接');
        updateButtonState('openAllLinks', { loading: false });
        return;
      }

      if (successItems.length > 10) {
        if (
          !window.confirm(
            `将要打开 ${successItems.length} 个标签页，确定继续吗？`,
          )
        ) {
          updateButtonState('openAllLinks', { loading: false });
          return;
        }
      }

      // 模拟短暂延迟以显示加载状态
      await new Promise(resolve => setTimeout(resolve, 300));

      // 分批打开链接，避免浏览器弹窗拦截
      let openedCount = 0;
      let blockedCount = 0;

      // 创建一个直接用户交互触发的函数，以获得更高的浏览器权限
      const openUrlsInBatches = (urls: string[], batchSize = 5) => {
        // 一次性创建所有需要的窗口，但分批次，每批次之间有更长的延迟
        const batches: string[][] = [];
        for (let i = 0; i < urls.length; i += batchSize) {
          batches.push(urls.slice(i, i + batchSize));
        }

        let currentBatchIndex = 0;

        const processNextBatch = () => {
          if (currentBatchIndex >= batches.length) {
            return;
          }

          const currentBatch = batches[currentBatchIndex++];

          // 立即连续打开当前批次的所有窗口
          currentBatch.forEach((url: string) => {
            try {
              const newWindow = window.open(
                url,
                '_blank',
                'noopener,noreferrer',
              );
              if (
                !newWindow ||
                newWindow.closed ||
                typeof newWindow.closed === 'undefined'
              ) {
                blockedCount++;
                console.warn(`链接被浏览器阻止: ${url}`);
              } else {
                openedCount++;
              }
            } catch (error) {
              blockedCount++;
              console.error(`打开链接失败: ${url}`, error);
            }
          });

          // 下一批次使用稍长的延迟
          if (currentBatchIndex < batches.length) {
            setTimeout(processNextBatch, 500);
          } else {
            // 全部完成后更新状态
            updateButtonState('openAllLinks', {
              loading: false,
              success: true,
            });
            resetButtonState('openAllLinks');

            if (logger) {
              if (blockedCount > 0) {
                logger.warning(
                  `已打开 ${openedCount} 个链接，${blockedCount} 个被浏览器阻止。请检查浏览器弹窗设置。`,
                );
              } else {
                logger.success(`已成功打开 ${openedCount} 个链接`);
              }
            }
          }
        };

        // 开始处理第一批
        processNextBatch();
      };

      // 收集所有需要打开的URL
      const urls = successItems
        .filter(item => item.playgroundUrl)
        .map(item => item.playgroundUrl as string);

      // 直接调用批量打开函数
      openUrlsInBatches(urls);
    } catch (error) {
      updateButtonState('openAllLinks', { loading: false, error: true });
      resetButtonState('openAllLinks');
      const errorMessage = `打开链接失败: ${(error as Error).message}`;
      console.error(errorMessage);
      if (logger) {
        logger.error(errorMessage);
      }
    }
  }, [history, updateButtonState, resetButtonState, logger]);

  // 处理重用查询
  const handleReuseQuery = useCallback(
    (query: string) => {
      if (onReuseQuery) {
        try {
          onReuseQuery(query);
          if (logger) {
            logger.success(`已重用查询: ${query}`);
          }
        } catch (error) {
          const errorMessage = `重用查询失败: ${(error as Error).message}`;
          console.error(errorMessage);
          if (logger) {
            logger.error(errorMessage);
          }
        }
      }
    },
    [onReuseQuery, logger],
  );

  // 渲染带状态的按钮
  const renderStatefulButton = (
    buttonKey: keyof typeof buttonStates,
    baseText: string,
    loadingText: string,
    successText: string,
    errorText: string,
    onClick: () => void,
    baseIconType: string,
    disabled = false,
  ) => {
    const state = buttonStates[buttonKey];
    const isDisabled = disabled || state.loading;

    let displayText = baseText;
    let iconType = baseIconType;
    let iconColor:
      | 'primary'
      | 'secondary'
      | 'success'
      | 'warning'
      | 'error'
      | 'processing'
      | 'neutral' = 'primary';

    if (state.loading) {
      displayText = loadingText;
      iconType = 'processing';
      iconColor = 'processing';
    } else if (state.success) {
      displayText = successText;
      iconType = 'check';
      iconColor = 'success';
    } else if (state.error) {
      displayText = errorText;
      iconType = 'error';
      iconColor = 'error';
    }

    // 根据状态确定按钮样式类
    let buttonClass = 'btn-authority btn-secondary-glass';
    if (state.success) {
      buttonClass = 'btn-authority btn-secondary-glass';
    } else if (state.error) {
      buttonClass = 'btn-authority btn-secondary-glass';
    }

    return (
      <button
        onClick={onClick}
        disabled={isDisabled}
        className={`${buttonClass} ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''} text-xs px-3 py-1`}
        title={state.error ? '操作失败，请重试' : ''}
        style={{
          transition: 'all 0.3s ease',
          transform: 'translateY(0)',
          boxShadow: '0 2px 5px rgba(0, 0, 0, 0.05)',
        }}
        onMouseEnter={e => {
          if (!isDisabled) {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.1)';
          }
        }}
        onMouseLeave={e => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';
        }}
      >
        <Icon
          type={iconType as any}
          size="sm"
          color={iconColor}
          className="mr-1"
          animate={state.loading}
        />
        {displayText}
      </button>
    );
  };

  return (
    <div className="h-full flex flex-col history-panel-container">
      {/* 样式已移至 history-panel.css */}

      {/* 简化的操作按钮栏 */}
      <div
        className="flex-shrink-0 p-4 border-b flex justify-between items-center"
        style={{
          background: 'var(--color-success-highlight)',
          borderColor: 'var(--color-gray-200)',
          borderRadius: '0',
        }}
      >
        <div className="text-sm" style={{ color: 'var(--color-gray-600)' }}>
          {history.length > 0 ? `共 ${history.length} 条记录` : '暂无记录'}
        </div>
        <div className="flex gap-2">
          {renderStatefulButton(
            'openAllLinks',
            '全部打开',
            '打开中...',
            '已打开',
            '打开失败',
            handleOpenAllLinks,
            'external-link',
            history.filter(
              item => item.status === 'success' && item.playgroundUrl,
            ).length === 0,
          )}
          {renderStatefulButton(
            'clearHistory',
            '清空',
            '清空中...',
            '已清空',
            '清空失败',
            handleClearHistory,
            'trash',
            history.length === 0,
          )}
        </div>
      </div>

      {/* 历史记录列表 - 紧凑布局优化 */}
      <div
        className="flex-1 overflow-y-auto history-list-container custom-scrollbar"
        style={{
          background: 'var(--color-gray-50)',
          padding: '8px',
          height: 'calc(100% - 80px)',
        }}
      >
        {isLoading ? (
          <div className="history-loading-state flex flex-col items-center justify-center h-full p-8">
            <div
              className="p-4 rounded-2xl mb-4"
              style={{
                background: 'var(--color-success-highlight)',
                border: '1px solid var(--color-primary-200)',
                boxShadow: 'var(--shadow-sm)',
              }}
            >
              <Icon type="processing" size="xl" color="processing" animate />
            </div>
            <p
              className="text-base font-semibold mb-2"
              style={{ color: 'var(--color-primary-600)' }}
            >
              加载历史记录中...
            </p>
            <p className="text-sm" style={{ color: 'var(--color-gray-500)' }}>
              请稍候，正在获取您的历史记录
            </p>
          </div>
        ) : history.length === 0 ? (
          <div className="history-empty-state flex flex-col items-center justify-center h-full p-8">
            <div
              className="p-6 rounded-2xl mb-6"
              style={{
                background:
                  'linear-gradient(135deg, rgba(156, 163, 175, 0.08) 0%, rgba(99, 102, 241, 0.05) 100%)',
                border: '2px solid rgba(99, 102, 241, 0.1)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
              }}
            >
              <Icon type="history" size="xl" color="neutral" />
            </div>
            <p
              className="text-lg font-semibold mb-2"
              style={{ color: 'var(--color-gray-600)' }}
            >
              暂无历史记录
            </p>
            <p
              className="text-sm text-center max-w-xs"
              style={{ color: 'var(--color-gray-400)' }}
            >
              开始处理查询后，历史记录将显示在这里
            </p>
          </div>
        ) : (
          <div className="space-y-2 compact-list-container">
            {history.map((item, index) => (
              <div
                key={item.id}
                className="history-record-item-modern compact-list-item"
                style={{ animationDelay: `${index * 0.08}s` }}
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div
                      className="flex-shrink-0 p-2 rounded-lg"
                      style={{
                        background:
                          item.status === 'success'
                            ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(22, 163, 74, 0.1) 100%)'
                            : 'linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%)',
                        border:
                          item.status === 'success'
                            ? '1px solid rgba(34, 197, 94, 0.2)'
                            : '1px solid rgba(239, 68, 68, 0.2)',
                        boxShadow:
                          item.status === 'success'
                            ? '0 2px 8px rgba(34, 197, 94, 0.15)'
                            : '0 2px 8px rgba(239, 68, 68, 0.15)',
                      }}
                    >
                      {item.status === 'success' && (
                        <Icon type="check" size="md" color="success" />
                      )}
                      {item.status === 'error' && (
                        <Icon type="error" size="md" color="error" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="history-record-time flex items-center gap-1.5 mb-1.5">
                        <Icon type="clock" size="sm" color="primary" />
                        <span
                          className="text-xs font-semibold"
                          style={{ color: 'var(--color-primary-600)' }}
                        >
                          {formatTime(item.startTime, 'time')}
                        </span>
                      </div>
                      <div className="history-record-content text-sm font-medium line-clamp-2 compact-content">
                        {item.query}
                      </div>
                    </div>
                  </div>
                </div>

                {/* URL显示 - 紧凑布局 */}
                {item.playgroundUrl && (
                  <div
                    className="mb-2 p-2 rounded-lg overflow-hidden"
                    style={{
                      background:
                        'linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%)',
                      border: '1px solid rgba(59, 130, 246, 0.15)',
                      boxShadow: '0 2px 4px rgba(59, 130, 246, 0.08)',
                    }}
                  >
                    <div
                      className="flex items-center gap-2 text-xs"
                      style={{ color: 'var(--color-blue-700)' }}
                    >
                      <Icon type="external-link" size="sm" color="secondary" />
                      <a
                        href={item.playgroundUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:underline truncate flex-1 font-medium transition-colors duration-200"
                        title={item.playgroundUrl}
                        style={{
                          color: 'var(--color-blue-600)',
                          maxWidth: 'calc(100% - 30px)',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                        }}
                      >
                        {item.playgroundUrl}
                      </a>
                    </div>
                  </div>
                )}

                {/* 操作按钮 - 紧凑布局 */}
                <div className="flex gap-1.5 justify-end compact-action-group">
                  {onReuseQuery && (
                    <button
                      onClick={() => handleReuseQuery(item.query)}
                      className="modern-action-btn"
                      title="重用此查询"
                      style={{
                        background:
                          'linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(168, 85, 247, 0.08) 100%)',
                        border: '1px solid rgba(99, 102, 241, 0.2)',
                        color: 'var(--color-primary-600)',
                        padding: '6px 12px',
                        borderRadius: '8px',
                        fontSize: '12px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                        cursor: 'pointer',
                      }}
                    >
                      <Icon type="refresh" size="sm" color="primary" />
                      重用
                    </button>
                  )}

                  {item.playgroundUrl && (
                    <button
                      onClick={() => window.open(item.playgroundUrl, '_blank')}
                      className="modern-action-btn"
                      title="打开 Playground"
                      style={{
                        background:
                          'linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(37, 99, 235, 0.08) 100%)',
                        border: '1px solid rgba(59, 130, 246, 0.2)',
                        color: 'var(--color-blue-600)',
                        padding: '6px 12px',
                        borderRadius: '8px',
                        fontSize: '12px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                        cursor: 'pointer',
                      }}
                    >
                      <Icon type="external-link" size="sm" color="secondary" />
                      打开
                    </button>
                  )}

                  {item.playgroundUrl && (
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(item.playgroundUrl || '');
                        if (logger) {
                          logger.success('已复制 URL 到剪贴板');
                        }
                      }}
                      className="modern-action-btn"
                      title="复制 URL"
                      style={{
                        background:
                          'linear-gradient(135deg, rgba(107, 114, 128, 0.08) 0%, rgba(75, 85, 99, 0.08) 100%)',
                        border: '1px solid rgba(107, 114, 128, 0.2)',
                        color: 'var(--color-gray-600)',
                        padding: '6px 12px',
                        borderRadius: '8px',
                        fontSize: '12px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                        transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                        cursor: 'pointer',
                      }}
                    >
                      <Icon type="copy" size="sm" color="neutral" />
                      复制
                    </button>
                  )}
                </div>
              </div>
            ))}

            {/* 分页控件 */}
            {totalPages > 1 && (
              <div className="mt-4 p-3 bg-white rounded-lg border border-gray-200 flex items-center justify-between">
                <div className="text-xs text-gray-500">
                  第 {currentPage} / {totalPages} 页
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() =>
                      setCurrentPage(prev => Math.max(1, prev - 1))
                    }
                    disabled={currentPage === 1}
                    className="px-2 py-1 text-xs rounded bg-gray-100 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors"
                  >
                    <Icon type="arrow-left" size="sm" color="neutral" />
                  </button>
                  <span className="px-2 py-1 text-xs text-gray-600">
                    {currentPage}
                  </span>
                  <button
                    onClick={() =>
                      setCurrentPage(prev => Math.min(totalPages, prev + 1))
                    }
                    disabled={currentPage === totalPages}
                    className="px-2 py-1 text-xs rounded bg-gray-100 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors"
                  >
                    <Icon type="arrow-right" size="sm" color="neutral" />
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoryPanel;
